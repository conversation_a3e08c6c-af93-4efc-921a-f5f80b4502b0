import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryCapitalFlowPage } from '@/pages/finance/flow/services';
import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { exportData } from '@/utils/exportData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import { PageContainer, ProFormInstance } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { Flex, Space } from 'antd';
import { useRef, useState } from 'react';
import { getTableColumns } from './config/TableColumns';
import type { CapitalEntity, TotalExpendAmountList, TotalIncomeAmountList } from './types/CapitalEntity.entity';

const CapitalFlow = () => {
  const intl = useIntl();
  const [pageParams, setPageParams] = useState<any>();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [totalIncomeAmountList, setTotalIncomeAmountList] = useState<TotalIncomeAmountList[]>([]);
  const [totalExpendAmountList, setTotalExpendAmountList] = useState<TotalExpendAmountList[]>([]);

  /**
   * 分页查询数据
   */
  const queryCapitalFlowByPage = async (params: Partial<CapitalEntity> & PageRequestParamsType) => {
    const queryResult = await queryCapitalFlowPage({
      ...params,
    });
    setTotalIncomeAmountList(queryResult?.data?.[0].totalIncomeAmountList || []);
    setTotalExpendAmountList(queryResult?.data?.[0].totalExpendAmountList || []);
    return { ...queryResult, data: queryResult?.data?.[0].finCapitalFlowPageRoList || [] };
  };

  return (
    <PageContainer>
      <FunProTable<CapitalEntity, any>
        onReset={() => {
          setPageParams({ pageSize: 10, pageNo: 1 });
        }}
        params={pageParams}
        scroll={{ x: 'max-content' }}
        requestPage={queryCapitalFlowByPage}
        actionRef={actionRef}
        columns={getTableColumns()}
        headerTitle={
          <Space direction='vertical' size={10}>
            <Space direction="horizontal" size={40} className='mb-1' >
              <Flex align="center" >
                <span className="text-[14px] text-[#00000] shrink-0">{intl.formatMessage({ id: 'finance.flow.totalIncome' })}</span>
                <span className="pl-4 text-[24px] font-medium text-primary">
                  {
                    totalIncomeAmountList.map((item, index) => (
                      <span key={item.currency}>{item.currencySymbol}{item.amount.toFixed(2)}
                        {index !== totalIncomeAmountList.length - 1 ? ';' : ''}
                      </span>
                    ))
                  }
                </span>
              </Flex>
              <Flex align="center">
                <span className="text-[14px] text-[#00000] shrink-0">{intl.formatMessage({ id: 'finance.flow.totalExpend' })}</span>
                <span className="pl-4 text-[24px] font-medium text-[#01A87C]">
                  {
                    totalExpendAmountList.map((item, index) => (
                      <span key={item.currency}>{item.currencySymbol}{item.amount.toFixed(2)}
                        {index !== totalExpendAmountList.length - 1 ? ';' : ''}
                      </span>
                    ))
                  }
                </span>
              </Flex>
            </Space>
            <Space direction="horizontal" size={40}>
              <AuthButton
                onClick={() => {
                  exportData({
                    systemId: 'GRIPX_STORE_SYS',
                    taskDesc: intl.formatMessage({ id: 'finance.flow.exportDescription' }),
                    moduleId: 'CAPITAL_FLOW_EXPORT',
                    params: formRef.current?.getFieldsValue()
                  });
                }}
              >{intl.formatMessage({ id: 'common.button.export' })}</AuthButton>
            </Space>
          </Space>
        }
      />
      {/* <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} /> */}
    </PageContainer>
  );
};

export default withKeepAlive(CapitalFlow);