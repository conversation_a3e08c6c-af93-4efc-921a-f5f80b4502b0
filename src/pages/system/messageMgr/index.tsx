import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table/lib';
import { useIntl } from '@umijs/max';
import { message } from 'antd';
import { useRef, useState } from 'react';
import CreateDrawer from './components/CreateDrawer';
import { PostListTableColumns } from './config/TableColumns';
import { createMsgPush, deletePushMsg, queryMsgPushPaged } from './services';
import { CreateDrawerProps } from './types/CreateDrawerProps';
import { MessageEntity } from './types/MessageEntity';

export default () => {
  const intl = useIntl();
  const t = (id: string) => intl.formatMessage({ id });
  const actionRef = useRef<ActionType>();
  const [createDrawerProps, setCreateDrawerProps] = useState<CreateDrawerProps<number, MessageEntity>>({
    visible: false,
    recordId: 0,
    readOnly: false,
    title: '',
  });

  /**
   * 详情
   * @param id
   */
  const handleDetailItem = (id?: number) => {
    setCreateDrawerProps({
      visible: true,
      recordId: id ?? 0,
      readOnly: true,
      title: t('system.messageMgr.detail.title'),
    });
  };
  /**
   * 作废
   * @param id
   */
  const handleUpdateItem = async (id?: number) => {
    try {
      const result = await deletePushMsg({ pushIdList: [id ?? 0] });
      if (result) {
        hideModal();
        actionRef.current?.reload();
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  /**
   * 关闭对话框
   */
  const hideModal = () => {
    setCreateDrawerProps({
      visible: false,
      recordId: 0,
      readOnly: false,
      title: '',
    });
  };


  /**
   * 新增
   * @param values
   */
  const handleSaveOrUpdate = async (values: MessageEntity) => {
    const { content } = values;
    if (content) {
      const div = document.createElement('div');
      div.innerHTML = content;
      // Use textContent as it's faster and simpler.
      // Replace non-breaking spaces with regular spaces, then trim.
      const text = (div.textContent || '').replace(/\u00A0/g, ' ').trim();
      if (!text && !div.querySelector('img')) {
        // Also check for images as content
        message.error(t('system.messageMgr.column.content.required'));
        return false;
      }
    } else {
      message.error(t('system.messageMgr.column.content.required'));
      return false;
    }

    try {
      const result = await createMsgPush({
        ...values,
        image: values.image?.[0]?.response?.data?.[0]
      });
      if (result) {
        hideModal();
        actionRef.current?.reload();
      }
      return true;
    } catch (error) {
      return false;
    }
  };

  return (
    <PageContainer>
      <FunProTable<MessageEntity, any>
        rowKey="id"
        requestPage={queryMsgPushPaged}
        scroll={{ x: 1300 }}
        actionRef={actionRef}
        columns={PostListTableColumns({
          handleDetailItem,
          handleUpdateItem,
          intl,
        })}
        headerTitle={
          <AuthButton
            type="primary"
            key="primary"
            authority="SYSTEM_STORE_ADD"
            onClick={() =>
              setCreateDrawerProps({
                visible: true,
                recordId: 0,
                readOnly: false,
                title: t('system.messageMgr.create.title'),
              })
            }
          >
            <PlusOutlined />
            {t('common.button.add')}
          </AuthButton>
        }
      />
      <CreateDrawer {...createDrawerProps} onOk={handleSaveOrUpdate} onCancel={hideModal} />
    </PageContainer>
  );
};
